'use client';

import { ChevronDown, PanelRightOpen, Search } from 'lucide-react'; // Added PanelRightOpen
import type React from 'react';
import { useState } from 'react';
import { PageItem } from './page-item';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Input } from '@/components/ui/input';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  useSidebar, // Added useSidebar
} from '@/components/ui/sidebar';
import {
  BlueprintFileItemType as StoreBlueprintFileItemType,
  useTakeoffStore,
} from '../store/takeoff-store';

// Use types from the store for consistency
type PdfFileType = StoreBlueprintFileItemType;

interface RightSidebarProps {
  files: PdfFileType[] | null;
  onSelectFile: (file: PdfFileType) => void;
}

export function RightSidebar({ files, onSelectFile }: RightSidebarProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const { selectedFile } = useTakeoffStore();
  const { toggleRightSidebar } = useSidebar(); // Added

  // TODO: Implement actual search filtering if needed
  const filteredFiles = files?.filter((file) =>
    file.fileName.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  return (
    <Sidebar
      side="right"
      className="border-l"
      style={{ '--sidebar-width': '22rem' } as React.CSSProperties}
    >
      <SidebarHeader className="h-14 border-b">
        <div className="flex items-center justify-between px-2">
          {' '}
          {/* Adjusted padding to px-2 like left sidebars */}
          <h2 className="text-sm font-medium">Files</h2>
          <div className="flex items-center gap-1">
            {' '}
            {/* Added a div to group buttons */}
            <Button
              onClick={toggleRightSidebar}
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              aria-label="Toggle sidebar"
            >
              <PanelRightOpen className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent className="p-3 space-y-3">
        <SidebarGroup className="space-y-3">
          {/* File Stats Card */}
          <Card className="py-2">
            <CardContent className="px-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">
                  Total sheets: {filteredFiles?.length ?? 0}
                </span>
                <div className="flex items-center gap-2">
                  {/* Placeholder for "Show only workable sheets" functionality if needed */}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Search Card */}
          <Card className="py-2 gap-2">
            <CardHeader className="px-3 pb-2">
              <CardTitle className="text-xs font-medium text-muted-foreground flex items-center gap-2">
                <Search className="h-4 w-4" />
                Search Files
              </CardTitle>
            </CardHeader>
            <CardContent className="px-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Search sheets by name..."
                  className="pl-9 h-9 rounded-lg border-border"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </CardContent>
          </Card>

          {/* Files List Card */}
          <Card className="py-2 gap-2">
            <CardHeader className="px-3">
              <CardTitle className="text-xs font-medium text-muted-foreground">
                Blueprint Files
              </CardTitle>
            </CardHeader>

            <CardContent className="px-3">
              <div className="space-y-2">
                {filteredFiles?.map((file, index) => (
                  <Collapsible
                    key={file.id}
                    defaultOpen={
                      selectedFile?.id === file.id ||
                      (!selectedFile && index === 0)
                    }
                    className="rounded-lg overflow-hidden border border-border/50 bg-card"
                  >
                    <CollapsibleTrigger
                      className={`flex w-full items-center justify-between rounded-lg hover:bg-muted/50 px-4 py-3 text-sm transition-colors ${
                        selectedFile?.id === file.id ? 'bg-muted' : ''
                      }`}
                      onClick={() => onSelectFile(file)} // Select file on trigger click
                    >
                      <div className="flex items-center gap-3 min-w-0">
                        <ChevronDown className="h-4 w-4 flex-shrink-0 transition-transform data-[state=closed]:-rotate-90" />
                        <span
                          className="font-medium truncate"
                          title={file.fileName}
                        >
                          {file.fileName}
                        </span>
                      </div>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="px-4 pb-3">
                      <div className="pt-2">
                        <SidebarGroupContent>
                          <SidebarMenu>
                            <PageItem key={file.id} fileData={file} />
                          </SidebarMenu>
                        </SidebarGroupContent>
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                ))}
                {(!filteredFiles || filteredFiles.length === 0) && (
                  <div className="px-4 py-8 text-sm text-muted-foreground text-center rounded-lg border border-dashed border-border/60 bg-muted/20">
                    <div className="space-y-2">
                      <div className="text-muted-foreground/60">📄</div>
                      <div>
                        {searchQuery
                          ? 'No files match your search.'
                          : 'No files available.'}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
