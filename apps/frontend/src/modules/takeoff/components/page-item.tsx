'use client';

import Image from 'next/image';
import { useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { Pencil } from 'lucide-react';

import { useBlueprintImages } from '../api/queries';
import { useUpdateImageScaleAndDimensions } from '../api/mutations'; // Updated import
import { SidebarMenuItem } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import {
  useTakeoffStore,
  ImageType as StoreImageType,
  BlueprintFileItemType as StoreBlueprintFileItemType,
} from '../store/takeoff-store';
import { Skeleton } from '@/components/ui/skeleton';
import { EditScaleModal } from './EditScaleModal'; // Import the new modal
import { queryKeys } from '@/lib/query-keys'; // For query invalidation
import { toast } from '@/lib/toast'; // For notifications
import { DEFAULT_PAPER_SIZE } from '@repo/component-summary';

// Use types from the store for consistency
type PdfFileType = StoreBlueprintFileItemType;
type ImageType = StoreImageType;
type ScaleType = NonNullable<ImageType['scale']>;
type DimensionsType = NonNullable<ImageType['dimensions']>; // Added dimensions type

interface PageItemProps {
  fileData: PdfFileType; // The parent blueprint file data
}

export function PageItem({ fileData }: PageItemProps) {
  const { data: imagesResponse, isLoading } = useBlueprintImages(fileData.id);
  const { selectedImage, setSelectedImage, isEditMode } = useTakeoffStore();

  const [isEditScaleModalOpen, setIsEditScaleModalOpen] = useState(false);
  const [currentImageForScaleEdit, setCurrentImageForScaleEdit] =
    useState<ImageType | null>(null);
  const [currentPageNumberForModal, setCurrentPageNumberForModal] =
    useState<number>(0);

  const queryClient = useQueryClient();
  // Updated to use the new mutation hook
  const { mutateAsync: updateImageScaleAndDimensions, isPending: isUpdating } =
    useUpdateImageScaleAndDimensions();
  const { setSelectedImage: setStoreSelectedImage } = useTakeoffStore();

  const handleSave = async (
    imageId: string,
    newScale: ScaleType,
    newDimensions: DimensionsType,
  ) => {
    if (!currentImageForScaleEdit) {
      toast.error('No image selected for update.');
      return;
    }
    try {
      await updateImageScaleAndDimensions({
        imageId,
        scale: newScale,
        dimensions: newDimensions,
      });
      toast.success('Scale and dimensions updated successfully!');

      // Optimistically update the store
      const updatedImage = {
        ...currentImageForScaleEdit,
        scale: newScale,
        dimensions: newDimensions,
      };
      setStoreSelectedImage(updatedImage as ImageType); // Update the store

      await queryClient.invalidateQueries({
        queryKey: queryKeys.takeoff.blueprintImages(fileData.id),
      });

      setIsEditScaleModalOpen(false);
      setCurrentImageForScaleEdit(null);
    } catch (error) {
      console.error('Failed to update scale and dimensions:', error);
      toast.error('Failed to update. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <SidebarMenuItem className="py-0">
        <div className="space-y-4">
          {[1, 2].map((i) => (
            <div
              key={i}
              className="rounded-xl border border-border/60 p-4 shadow-sm"
            >
              <div className="flex items-start gap-4">
                <Skeleton className="h-[80px] w-[80px] rounded-lg" />
                <div className="flex-1 space-y-3">
                  <div className="space-y-1.5">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                  <div className="bg-muted/40 rounded-lg p-3 space-y-2 border border-border/30">
                    <Skeleton className="h-3 w-full" />
                    <Skeleton className="h-3 w-2/3" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </SidebarMenuItem>
    );
  }

  return (
    <SidebarMenuItem className="py-0">
      <div className="space-y-4">
        {imagesResponse?.data?.map((image, index) => (
          <div
            key={image.id}
            className={`relative group cursor-pointer rounded-xl border transition-all duration-200 overflow-hidden shadow-sm ${
              selectedImage?.id === image.id
                ? 'bg-primary/5 border-primary shadow-md ring-1 ring-primary/20'
                : 'border-border/60 hover:border-border hover:bg-muted/30 hover:shadow-md'
            }`}
            onClick={() => setSelectedImage(image as ImageType)}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ')
                setSelectedImage(image as ImageType);
            }}
          >
            <div className="p-4">
              <div className="flex items-start gap-4">
                <div className="min-w-[80px] w-[80px] h-[80px] flex-shrink-0">
                  {image.path && (
                    <Image
                      src={image.path}
                      alt={image.filename || `Page ${index + 1}`}
                      width={80}
                      height={80}
                      className="border border-border/50 rounded-lg object-cover w-full h-full shadow-sm"
                    />
                  )}
                </div>
                <div className="flex flex-col justify-start min-w-0 flex-grow space-y-3">
                  <div className="space-y-1.5">
                    <div className="text-sm font-semibold truncate text-foreground">
                      Page {index + 1}:{' '}
                      {image.filename?.replace(/\.[^/.]+$/, '') ||
                        'Untitled Sheet'}
                    </div>
                    <div className="text-xs text-muted-foreground/70 truncate">
                      {image.filename || 'No filename'}
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Button
                      variant="ghost"
                      size="icon"
                      className={`h-8 w-8 flex-shrink-0 rounded-lg transition-colors ${
                        isEditMode
                          ? 'hover:bg-primary/10 hover:text-primary'
                          : 'opacity-40 cursor-not-allowed'
                      }`}
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent triggering onClick of parent div
                        if (!isEditMode) {
                          toast.error(
                            'Edit mode required to change scale and dimensions',
                          );
                          return;
                        }
                        setCurrentImageForScaleEdit(image as ImageType);
                        setCurrentPageNumberForModal(index + 1); // Set page number for modal title
                        setIsEditScaleModalOpen(true);
                      }}
                      aria-label={
                        isEditMode
                          ? 'Edit scale and dimensions'
                          : 'Edit scale and dimensions (Edit mode required)'
                      }
                      title={
                        isEditMode
                          ? 'Edit scale and dimensions'
                          : 'Edit mode required'
                      }
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <div className="flex flex-col space-y-2 min-w-0 flex-1">
                      <div className="bg-muted/40 rounded-lg p-3 space-y-2 border border-border/30">
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-medium text-muted-foreground">
                            Scale
                          </span>
                          {image.scale ? (
                            <span className="text-xs font-mono text-foreground bg-background px-2 py-1 rounded border">
                              {image.scale.num_metric} {image.scale.num_unit} :{' '}
                              {image.scale.den_metric.toFixed(2)}{' '}
                              {image.scale.den_unit}
                            </span>
                          ) : (
                            <span className="text-xs italic text-muted-foreground/60 bg-muted px-2 py-1 rounded">
                              Not set
                            </span>
                          )}
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-medium text-muted-foreground">
                            Size
                          </span>
                          {image.dimensions ? (
                            <span className="text-xs font-mono text-foreground bg-background px-2 py-1 rounded border">
                              {image.dimensions.width}" ×{' '}
                              {image.dimensions.height}"
                            </span>
                          ) : (
                            <span className="text-xs font-mono text-foreground bg-background px-2 py-1 rounded border">
                              {DEFAULT_PAPER_SIZE.width}" ×{' '}
                              {DEFAULT_PAPER_SIZE.height}"
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
        {(!imagesResponse?.data || imagesResponse.data.length === 0) &&
          !isLoading && (
            <div className="p-8 text-center text-sm text-muted-foreground rounded-xl border border-dashed border-border/60 bg-muted/20 shadow-sm">
              <div className="space-y-3">
                <div className="text-2xl text-muted-foreground/60">📄</div>
                <div className="font-medium">
                  No images found for this file.
                </div>
                <div className="text-xs text-muted-foreground/70">
                  Images will appear here once they are processed.
                </div>
              </div>
            </div>
          )}
      </div>

      {currentImageForScaleEdit && (
        <EditScaleModal
          isOpen={isEditScaleModalOpen}
          onClose={() => {
            setIsEditScaleModalOpen(false);
            setCurrentImageForScaleEdit(null); // Clear image when closing
          }}
          image={currentImageForScaleEdit}
          pageNumber={currentPageNumberForModal}
          onSave={handleSave} // Updated to handleSave
          isSaving={isUpdating} // Updated to isUpdating
        />
      )}
    </SidebarMenuItem>
  );
}
